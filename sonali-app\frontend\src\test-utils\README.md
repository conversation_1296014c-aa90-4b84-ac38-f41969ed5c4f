# Testing Guide

This document outlines the testing strategy and utilities for the Sonali App frontend.

## Testing Stack

- **Testing Framework**: Jest (via Vitest)
- **Testing Library**: React Testing Library
- **User Interactions**: @testing-library/user-event
- **Accessibility Testing**: Custom utilities + manual checks

## Test Structure

```
src/
├── components/
│   └── ui/
│       └── __tests__/
│           ├── Button.test.tsx
│           ├── Input.test.tsx
│           └── ...
├── contexts/
│   └── __tests__/
│       ├── AuthContext.test.tsx
│       └── ThemeContext.test.tsx
├── pages/
│   └── __tests__/
│       ├── Login.test.tsx
│       └── ...
└── test-utils/
    ├── setup.ts
    ├── accessibility.ts
    └── README.md
```

## Running Tests

```bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test Button.test.tsx
```

## Testing Utilities

### Setup (`setup.ts`)

Global test setup including:
- Jest DOM matchers
- Mock implementations for browser APIs
- Global mocks for localStorage, sessionStorage
- Mock implementations for IntersectionObserver, ResizeObserver

### Accessibility Testing (`accessibility.ts`)

Custom utilities for accessibility validation:

```tsx
import { runAccessibilityChecks } from '../test-utils/accessibility';

it('passes accessibility checks', () => {
  const { container } = render(<MyComponent />);
  const results = runAccessibilityChecks(container);
  
  expect(results.hasIssues).toBe(false);
  expect(results.issueCount).toBe(0);
});
```

## Testing Patterns

### Component Testing

```tsx
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { Button } from '../Button';

describe('Button Component', () => {
  it('renders with correct text', () => {
    render(<Button>Click me</Button>);
    expect(screen.getByRole('button', { name: /click me/i })).toBeInTheDocument();
  });

  it('handles click events', async () => {
    const user = userEvent.setup();
    const handleClick = jest.fn();
    
    render(<Button onClick={handleClick}>Click me</Button>);
    
    await user.click(screen.getByRole('button'));
    expect(handleClick).toHaveBeenCalledTimes(1);
  });
});
```

### Context Testing

```tsx
import { render, screen } from '@testing-library/react';
import { AuthProvider, useAuth } from '../AuthContext';

const TestComponent = () => {
  const { isAuthenticated } = useAuth();
  return <div>{isAuthenticated ? 'Logged in' : 'Logged out'}</div>;
};

describe('AuthContext', () => {
  it('provides authentication state', () => {
    render(
      <AuthProvider>
        <TestComponent />
      </AuthProvider>
    );
    
    expect(screen.getByText('Logged out')).toBeInTheDocument();
  });
});
```

### Form Testing

```tsx
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

describe('Login Form', () => {
  it('validates required fields', async () => {
    const user = userEvent.setup();
    render(<LoginForm />);

    const submitButton = screen.getByRole('button', { name: /submit/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/email is required/i)).toBeInTheDocument();
    });
  });

  it('submits form with valid data', async () => {
    const user = userEvent.setup();
    const onSubmit = jest.fn();
    
    render(<LoginForm onSubmit={onSubmit} />);

    await user.type(screen.getByLabelText(/email/i), '<EMAIL>');
    await user.type(screen.getByLabelText(/password/i), 'password123');
    await user.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(onSubmit).toHaveBeenCalledWith({
        email: '<EMAIL>',
        password: 'password123',
      });
    });
  });
});
```

## Accessibility Testing

### Manual Checks

1. **Keyboard Navigation**
   - Tab through all interactive elements
   - Ensure logical tab order
   - Test Enter/Space key activation

2. **Screen Reader Testing**
   - Use NVDA, JAWS, or VoiceOver
   - Verify all content is announced
   - Check for proper landmarks

3. **Color Contrast**
   - Use browser dev tools or online tools
   - Ensure WCAG AA compliance (4.5:1 ratio)

### Automated Checks

```tsx
import { runAccessibilityChecks } from '../test-utils/accessibility';

describe('Accessibility', () => {
  it('has proper ARIA labels', () => {
    const { container } = render(<MyComponent />);
    const results = runAccessibilityChecks(container);
    
    expect(results.ariaLabels).toHaveLength(0); // No issues
  });

  it('uses semantic HTML', () => {
    const { container } = render(<MyComponent />);
    const results = runAccessibilityChecks(container);
    
    expect(results.semanticHTML).toHaveLength(0); // No issues
  });
});
```

## Mocking Strategies

### API Calls

```tsx
// Mock the entire service
jest.mock('../services/authService', () => ({
  authService: {
    login: jest.fn(),
    logout: jest.fn(),
    isAuthenticated: jest.fn(),
  },
}));

// Use the mock in tests
const mockAuthService = require('../services/authService').authService;

beforeEach(() => {
  mockAuthService.login.mockResolvedValue({ user: mockUser });
});
```

### Router

```tsx
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ pathname: '/test' }),
}));
```

### Local Storage

```tsx
beforeEach(() => {
  localStorage.clear();
  localStorage.setItem.mockClear();
});
```

## Best Practices

1. **Test Behavior, Not Implementation**
   - Focus on what users see and do
   - Avoid testing internal state or methods

2. **Use Semantic Queries**
   - Prefer `getByRole`, `getByLabelText`
   - Avoid `getByTestId` unless necessary

3. **Async Testing**
   - Use `waitFor` for async operations
   - Use `findBy` queries for elements that appear later

4. **Accessibility First**
   - Include accessibility checks in all component tests
   - Test keyboard navigation
   - Verify ARIA attributes

5. **Realistic User Interactions**
   - Use `userEvent` instead of `fireEvent`
   - Test complete user flows

## Coverage Goals

- **Components**: 90%+ coverage
- **Contexts**: 95%+ coverage
- **Pages**: 85%+ coverage
- **Utilities**: 95%+ coverage

## Continuous Integration

Tests run automatically on:
- Pull requests
- Main branch commits
- Release builds

Coverage reports are generated and tracked over time.
