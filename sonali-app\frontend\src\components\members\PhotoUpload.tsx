import React, { useState, useRef, useCallback } from 'react';
import { But<PERSON>, Alert } from '../ui';

interface PhotoUploadProps {
  value?: string;
  onChange: (photoUrl: string | null) => void;
  disabled?: boolean;
  className?: string;
}

export const PhotoUpload: React.FC<PhotoUploadProps> = ({
  value,
  onChange,
  disabled = false,
  className = ''
}) => {
  const [preview, setPreview] = useState<string | null>(value || null);
  const [uploading, setUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    // Check file type
    if (!file.type.startsWith('image/')) {
      return 'Please select an image file';
    }

    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return 'File size must be less than 5MB';
    }

    // Check image dimensions
    return new Promise<string | null>((resolve) => {
      const img = new Image();
      img.onload = () => {
        if (img.width < 200 || img.height < 200) {
          resolve('Image must be at least 200x200 pixels');
        } else {
          resolve(null);
        }
      };
      img.onerror = () => resolve('Invalid image file');
      img.src = URL.createObjectURL(file);
    }) as any;
  };

  const processFile = async (file: File) => {
    setError(null);
    setUploading(true);

    try {
      // Validate file
      const validationError = await validateFile(file);
      if (validationError) {
        setError(validationError);
        return;
      }

      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setPreview(result);
      };
      reader.readAsDataURL(file);

      // In a real implementation, you would upload to your server here
      // For now, we'll simulate an upload and use the data URL
      setTimeout(() => {
        const reader = new FileReader();
        reader.onload = (e) => {
          const result = e.target?.result as string;
          onChange(result);
          setUploading(false);
        };
        reader.readAsDataURL(file);
      }, 1000);

    } catch (err) {
      setError('Failed to process image');
      setUploading(false);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      processFile(file);
    }
  };

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
    
    const file = e.dataTransfer.files[0];
    if (file) {
      processFile(file);
    }
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragOver(false);
  }, []);

  const handleRemove = () => {
    setPreview(null);
    onChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleClick = () => {
    if (!disabled) {
      fileInputRef.current?.click();
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="flex items-center gap-4">
        {/* Photo Preview */}
        <div className="relative">
          <div 
            className={`
              w-32 h-32 rounded-lg border-2 border-dashed border-gray-300 dark:border-gray-600
              flex items-center justify-center cursor-pointer transition-colors
              ${dragOver ? 'border-primary-500 bg-primary-50 dark:bg-primary-900/20' : ''}
              ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary-400'}
              ${preview ? 'border-solid' : ''}
            `}
            onClick={handleClick}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
          >
            {preview ? (
              <img
                src={preview}
                alt="Member photo"
                className="w-full h-full object-cover rounded-lg"
              />
            ) : (
              <div className="text-center">
                <div className="text-4xl mb-2">📷</div>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {uploading ? 'Uploading...' : 'Add Photo'}
                </p>
              </div>
            )}
          </div>

          {preview && !disabled && (
            <button
              type="button"
              onClick={(e) => {
                e.stopPropagation();
                handleRemove();
              }}
              className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
            >
              ✕
            </button>
          )}

          {uploading && (
            <div className="absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center">
              <div className="w-8 h-8 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
            </div>
          )}
        </div>

        {/* Upload Instructions */}
        <div className="flex-1">
          <h4 className="font-medium text-gray-900 dark:text-white mb-2">
            Member Photo
          </h4>
          <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
            Upload a clear photo of the member. The photo will be used for identification purposes.
          </p>
          <div className="space-y-2">
            <Button
              type="button"
              variant="secondary"
              size="sm"
              onClick={handleClick}
              disabled={disabled || uploading}
            >
              {uploading ? 'Uploading...' : preview ? 'Change Photo' : 'Choose Photo'}
            </Button>
            {preview && (
              <Button
                type="button"
                variant="secondary"
                size="sm"
                onClick={handleRemove}
                disabled={disabled}
                className="ml-2"
              >
                Remove
              </Button>
            )}
          </div>
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-2">
            Supported formats: JPG, PNG, GIF. Max size: 5MB. Min dimensions: 200x200px.
          </p>
        </div>
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
        disabled={disabled}
      />

      {/* Error message */}
      {error && (
        <Alert type="error" title="Upload Error" description={error} />
      )}

      {/* Drag and drop instructions */}
      <div className="text-center">
        <p className="text-xs text-gray-500 dark:text-gray-400">
          You can also drag and drop an image file here
        </p>
      </div>
    </div>
  );
};

export default PhotoUpload;
