import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import {
  Form,
  FormGroup,
  Input,
  Select,
  Textarea,
  Button,
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Alert
} from '../ui';
import { PhotoUpload } from './PhotoUpload';
import { MemberSearch } from './MemberSearch';
import { CreateMemberData, MemberSearchResult, Branch } from '../../types';
import MemberService from '../../services/memberService';

interface MemberRegistrationFormProps {
  onSuccess?: (member: any) => void;
  onCancel?: () => void;
  initialData?: Partial<CreateMemberData>;
  branches?: Branch[];
  className?: string;
}

const RELIGIONS = [
  'Islam',
  'Hinduism', 
  'Christianity',
  'Buddhism',
  'Others'
];

const BLOOD_GROUPS = [
  'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'
];

export const MemberRegistrationForm: React.FC<MemberRegistrationFormProps> = ({
  onSuccess,
  onCancel,
  initialData,
  branches = [],
  className = ''
}) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [generatingId, setGeneratingId] = useState(false);
  const [reference, setReference] = useState<MemberSearchResult | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    reset,
    trigger
  } = useForm<CreateMemberData>({
    defaultValues: {
      religion: 'Islam',
      ...initialData
    }
  });

  const watchedBranchId = watch('branchId');
  const watchedMemberId = watch('memberId');
  const watchedNidNumber = watch('nidNumber');

  // Generate member ID when branch changes
  useEffect(() => {
    if (watchedBranchId && !initialData?.memberId) {
      generateMemberId();
    }
  }, [watchedBranchId]);

  const generateMemberId = async () => {
    if (generatingId) return;
    
    setGeneratingId(true);
    try {
      const memberId = await MemberService.generateMemberId(watchedBranchId);
      setValue('memberId', memberId);
      await trigger('memberId');
    } catch (error) {
      console.error('Error generating member ID:', error);
    } finally {
      setGeneratingId(false);
    }
  };

  // Validate member ID uniqueness
  const validateMemberId = async (memberId: string) => {
    if (!memberId) return 'Member ID is required';
    
    try {
      const isValid = await MemberService.validateMemberId(memberId);
      return isValid || 'Member ID already exists';
    } catch (error) {
      return 'Error validating member ID';
    }
  };

  // Validate NID uniqueness
  const validateNid = async (nidNumber: string) => {
    if (!nidNumber) return 'NID number is required';
    
    if (nidNumber.length !== 13 && nidNumber.length !== 17) {
      return 'NID number must be 13 or 17 digits';
    }
    
    try {
      const isValid = await MemberService.validateNid(nidNumber);
      return isValid || 'NID number already exists';
    } catch (error) {
      return 'Error validating NID number';
    }
  };

  const onSubmit = async (data: CreateMemberData) => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      // Add reference ID if selected
      if (reference) {
        data.referenceId = reference.id;
      }

      const member = await MemberService.createMember(data);
      
      setSuccess('Member registered successfully!');
      
      if (onSuccess) {
        onSuccess(member);
      } else {
        // Reset form for new registration
        reset();
        setReference(null);
      }
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to register member');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span>👤</span>
            Member Registration
          </CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert type="error" title="Registration Failed" description={error} className="mb-6" />
          )}
          
          {success && (
            <Alert type="success" title="Success" description={success} className="mb-6" />
          )}

          <Form onSubmit={handleSubmit(onSubmit)} spacing="lg">
            {/* Basic Information */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Basic Information
                </h3>

                <FormGroup>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Input
                      label="Member ID"
                      {...register('memberId', { 
                        required: 'Member ID is required',
                        validate: validateMemberId
                      })}
                      error={errors.memberId?.message}
                      disabled={generatingId}
                      icon={generatingId ? '⏳' : undefined}
                    />
                    <Select
                      label="Branch"
                      {...register('branchId', { required: 'Branch is required' })}
                      error={errors.branchId?.message}
                      required
                    >
                      <option value="">Select Branch</option>
                      {branches.map(branch => (
                        <option key={branch.id} value={branch.id}>
                          {branch.name}
                        </option>
                      ))}
                    </Select>
                  </div>
                </FormGroup>

                <Input
                  label="Full Name"
                  {...register('name', { required: 'Name is required' })}
                  error={errors.name?.message}
                  required
                />

                <Input
                  label="Father's/Husband's Name"
                  {...register('fatherOrHusbandName', { required: 'Father\'s/Husband\'s name is required' })}
                  error={errors.fatherOrHusbandName?.message}
                  required
                />

                <Input
                  label="Mother's Name"
                  {...register('motherName', { required: 'Mother\'s name is required' })}
                  error={errors.motherName?.message}
                  required
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <Input
                    label="Date of Birth"
                    type="date"
                    {...register('dateOfBirth', { required: 'Date of birth is required' })}
                    error={errors.dateOfBirth?.message}
                    required
                  />
                  <Select
                    label="Religion"
                    {...register('religion', { required: 'Religion is required' })}
                    error={errors.religion?.message}
                    required
                  >
                    {RELIGIONS.map(religion => (
                      <option key={religion} value={religion}>
                        {religion}
                      </option>
                    ))}
                  </Select>
                </div>
              </div>

              {/* Photo Upload */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Photo & Contact
                </h3>

                <PhotoUpload
                  value={watch('photo')}
                  onChange={(photoUrl) => setValue('photo', photoUrl || undefined)}
                />

                <Input
                  label="Phone Number"
                  {...register('phoneNumber', { 
                    required: 'Phone number is required',
                    pattern: {
                      value: /^(\+88)?01[3-9]\d{8}$/,
                      message: 'Please enter a valid Bangladeshi phone number'
                    }
                  })}
                  error={errors.phoneNumber?.message}
                  placeholder="+8801XXXXXXXXX"
                  required
                />

                <Select
                  label="Blood Group"
                  {...register('bloodGroup')}
                  error={errors.bloodGroup?.message}
                >
                  <option value="">Select Blood Group</option>
                  {BLOOD_GROUPS.map(group => (
                    <option key={group} value={group}>
                      {group}
                    </option>
                  ))}
                </Select>
              </div>
            </div>

            {/* Address Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                Address Information
              </h3>

              <Textarea
                label="Present Address"
                {...register('presentAddress', { required: 'Present address is required' })}
                error={errors.presentAddress?.message}
                rows={3}
                required
              />

              <Textarea
                label="Permanent Address"
                {...register('permanentAddress', { required: 'Permanent address is required' })}
                error={errors.permanentAddress?.message}
                rows={3}
                required
              />
            </div>

            {/* Additional Information */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Additional Information
                </h3>

                <Input
                  label="NID Number"
                  {...register('nidNumber', { 
                    required: 'NID number is required',
                    validate: validateNid
                  })}
                  error={errors.nidNumber?.message}
                  placeholder="13 or 17 digit NID number"
                  required
                />

                <Input
                  label="Occupation"
                  {...register('occupation', { required: 'Occupation is required' })}
                  error={errors.occupation?.message}
                  required
                />
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white border-b border-gray-200 dark:border-gray-700 pb-2">
                  Reference
                </h3>

                <MemberSearch
                  label="Reference Member (Optional)"
                  value={reference}
                  onChange={setReference}
                  branchId={watchedBranchId}
                  placeholder="Search for reference member..."
                />
              </div>
            </div>

            {/* Form Actions */}
            <div className="flex gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
              <Button
                type="submit"
                variant="primary"
                disabled={loading}
                className="flex-1 md:flex-none"
              >
                {loading ? 'Registering...' : 'Register Member'}
              </Button>
              
              {onCancel && (
                <Button
                  type="button"
                  variant="secondary"
                  onClick={onCancel}
                  disabled={loading}
                >
                  Cancel
                </Button>
              )}
              
              <Button
                type="button"
                variant="secondary"
                onClick={() => {
                  reset();
                  setReference(null);
                  setError(null);
                  setSuccess(null);
                }}
                disabled={loading}
              >
                Reset
              </Button>
            </div>
          </Form>
        </CardContent>
      </Card>
    </div>
  );
};

export default MemberRegistrationForm;
