import React, { useState, useEffect } from 'react';
import {
  Card,
  CardHeader,
  CardTitle,
  CardContent,
  Input,
  Select,
  Button,
  Table,
  Loading,
  Alert
} from '../ui';
import { Member, MemberListQuery, Branch, PaginatedResponse } from '../../types';
import MemberService from '../../services/memberService';

interface MemberListProps {
  onMemberSelect?: (member: Member) => void;
  onMemberEdit?: (member: Member) => void;
  onMemberDelete?: (member: Member) => void;
  branches?: Branch[];
  className?: string;
  showActions?: boolean;
  selectable?: boolean;
  selectedMembers?: string[];
  onSelectionChange?: (memberIds: string[]) => void;
}

export const MemberList: React.FC<MemberListProps> = ({
  onMemberSelect,
  onMemberEdit,
  onMemberDelete,
  branches = [],
  className = '',
  showActions = true,
  selectable = false,
  selectedMembers = [],
  onSelectionChange
}) => {
  const [members, setMembers] = useState<Member[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0
  });

  // Filters
  const [filters, setFilters] = useState<MemberListQuery>({
    page: 1,
    limit: 20,
    search: '',
    branchId: '',
    isActive: undefined,
    sortBy: 'createdAt',
    sortOrder: 'desc'
  });

  // Load members
  const loadMembers = async () => {
    setLoading(true);
    setError(null);

    try {
      const response: PaginatedResponse<Member> = await MemberService.getMembers(filters);
      setMembers(response.data);
      setPagination(response.pagination);
    } catch (err: any) {
      setError(err.response?.data?.message || 'Failed to load members');
    } finally {
      setLoading(false);
    }
  };

  // Load members when filters change
  useEffect(() => {
    loadMembers();
  }, [filters]);

  // Handle filter changes
  const handleFilterChange = (key: keyof MemberListQuery, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: key !== 'page' ? 1 : value // Reset to first page when other filters change
    }));
  };

  // Handle search with debouncing
  const [searchTimeout, setSearchTimeout] = useState<NodeJS.Timeout>();
  const handleSearchChange = (value: string) => {
    if (searchTimeout) clearTimeout(searchTimeout);
    
    const timeout = setTimeout(() => {
      handleFilterChange('search', value);
    }, 500);
    
    setSearchTimeout(timeout);
  };

  // Handle selection
  const handleSelectAll = (checked: boolean) => {
    if (!onSelectionChange) return;
    
    if (checked) {
      const allIds = members.map(member => member.id);
      onSelectionChange([...new Set([...selectedMembers, ...allIds])]);
    } else {
      const currentIds = members.map(member => member.id);
      onSelectionChange(selectedMembers.filter(id => !currentIds.includes(id)));
    }
  };

  const handleSelectMember = (memberId: string, checked: boolean) => {
    if (!onSelectionChange) return;
    
    if (checked) {
      onSelectionChange([...selectedMembers, memberId]);
    } else {
      onSelectionChange(selectedMembers.filter(id => id !== memberId));
    }
  };

  // Export members
  const handleExport = async () => {
    try {
      const blob = await MemberService.exportMembers(filters);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `members-${new Date().toISOString().split('T')[0]}.xlsx`;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting members:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-BD');
  };

  const isAllSelected = members.length > 0 && members.every(member => selectedMembers.includes(member.id));
  const isSomeSelected = members.some(member => selectedMembers.includes(member.id));

  const columns = [
    ...(selectable ? [{
      key: 'select',
      title: (
        <input
          type="checkbox"
          checked={isAllSelected}
          ref={(input) => {
            if (input) input.indeterminate = isSomeSelected && !isAllSelected;
          }}
          onChange={(e) => handleSelectAll(e.target.checked)}
          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
        />
      ),
      render: (member: Member) => (
        <input
          type="checkbox"
          checked={selectedMembers.includes(member.id)}
          onChange={(e) => handleSelectMember(member.id, e.target.checked)}
          className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
        />
      )
    }] : []),
    {
      key: 'photo',
      title: 'Photo',
      render: (member: Member) => (
        <div className="w-10 h-10 rounded-full overflow-hidden bg-gray-200 dark:bg-gray-600">
          {member.photo ? (
            <img src={member.photo} alt={member.name} className="w-full h-full object-cover" />
          ) : (
            <div className="w-full h-full flex items-center justify-center text-gray-500 dark:text-gray-400">
              👤
            </div>
          )}
        </div>
      )
    },
    {
      key: 'memberId',
      title: 'Member ID',
      sortable: true
    },
    {
      key: 'name',
      title: 'Name',
      sortable: true
    },
    {
      key: 'phoneNumber',
      title: 'Phone',
      sortable: true
    },
    {
      key: 'branchName',
      title: 'Branch',
      render: (member: Member) => member.branchName || 'N/A'
    },
    {
      key: 'occupation',
      title: 'Occupation',
      sortable: true
    },
    {
      key: 'createdAt',
      title: 'Registered',
      sortable: true,
      render: (member: Member) => formatDate(member.createdAt)
    },
    {
      key: 'isActive',
      title: 'Status',
      render: (member: Member) => (
        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
          member.isActive 
            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
            : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
        }`}>
          {member.isActive ? 'Active' : 'Inactive'}
        </span>
      )
    },
    ...(showActions ? [{
      key: 'actions',
      title: 'Actions',
      render: (member: Member) => (
        <div className="flex gap-2">
          {onMemberSelect && (
            <Button
              size="sm"
              variant="secondary"
              onClick={() => onMemberSelect(member)}
            >
              View
            </Button>
          )}
          {onMemberEdit && (
            <Button
              size="sm"
              variant="secondary"
              onClick={() => onMemberEdit(member)}
            >
              Edit
            </Button>
          )}
          {onMemberDelete && (
            <Button
              size="sm"
              variant="secondary"
              onClick={() => onMemberDelete(member)}
              className="text-red-600 hover:text-red-700"
            >
              Delete
            </Button>
          )}
        </div>
      )
    }] : [])
  ];

  return (
    <div className={className}>
      <Card>
        <CardHeader>
          <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <CardTitle className="flex items-center gap-2">
              <span>👥</span>
              Members ({pagination.total})
            </CardTitle>
            
            <div className="flex gap-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={handleExport}
                disabled={loading}
              >
                📊 Export
              </Button>
              
              {selectable && selectedMembers.length > 0 && (
                <span className="text-sm text-gray-600 dark:text-gray-400 flex items-center">
                  {selectedMembers.length} selected
                </span>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent>
          {/* Filters */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Input
              placeholder="Search members..."
              onChange={(e) => handleSearchChange(e.target.value)}
              icon="🔍"
            />
            
            <Select
              value={filters.branchId || ''}
              onChange={(e) => handleFilterChange('branchId', e.target.value)}
            >
              <option value="">All Branches</option>
              {branches.map(branch => (
                <option key={branch.id} value={branch.id}>
                  {branch.name}
                </option>
              ))}
            </Select>
            
            <Select
              value={filters.isActive === undefined ? '' : filters.isActive.toString()}
              onChange={(e) => handleFilterChange('isActive', 
                e.target.value === '' ? undefined : e.target.value === 'true'
              )}
            >
              <option value="">All Status</option>
              <option value="true">Active</option>
              <option value="false">Inactive</option>
            </Select>
            
            <Select
              value={`${filters.sortBy}-${filters.sortOrder}`}
              onChange={(e) => {
                const [sortBy, sortOrder] = e.target.value.split('-');
                handleFilterChange('sortBy', sortBy);
                handleFilterChange('sortOrder', sortOrder);
              }}
            >
              <option value="createdAt-desc">Newest First</option>
              <option value="createdAt-asc">Oldest First</option>
              <option value="name-asc">Name A-Z</option>
              <option value="name-desc">Name Z-A</option>
              <option value="memberId-asc">Member ID A-Z</option>
              <option value="memberId-desc">Member ID Z-A</option>
            </Select>
          </div>

          {/* Error */}
          {error && (
            <Alert type="error" title="Error" description={error} className="mb-4" />
          )}

          {/* Loading */}
          {loading ? (
            <Loading size="lg" />
          ) : (
            <>
              {/* Table */}
              <Table
                columns={columns}
                data={members}
                onSort={(key, order) => {
                  handleFilterChange('sortBy', key);
                  handleFilterChange('sortOrder', order);
                }}
                sortBy={filters.sortBy}
                sortOrder={filters.sortOrder}
              />

              {/* Pagination */}
              {pagination.totalPages > 1 && (
                <div className="flex items-center justify-between mt-6">
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                    {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                    {pagination.total} members
                  </div>
                  
                  <div className="flex gap-2">
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => handleFilterChange('page', pagination.page - 1)}
                      disabled={pagination.page <= 1}
                    >
                      Previous
                    </Button>
                    
                    <span className="flex items-center px-3 text-sm">
                      Page {pagination.page} of {pagination.totalPages}
                    </span>
                    
                    <Button
                      variant="secondary"
                      size="sm"
                      onClick={() => handleFilterChange('page', pagination.page + 1)}
                      disabled={pagination.page >= pagination.totalPages}
                    >
                      Next
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default MemberList;
