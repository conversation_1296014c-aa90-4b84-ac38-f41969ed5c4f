import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { BrowserRouter } from 'react-router-dom';
import { Login } from '../Login';
import { AuthProvider } from '../../contexts/AuthContext';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { runAccessibilityChecks } from '../../test-utils/accessibility';

// Mock the auth service
jest.mock('../../services/authService', () => ({
  authService: {
    isAuthenticated: jest.fn(() => false),
    login: jest.fn(),
  },
}));

// Mock react-router-dom hooks
const mockNavigate = jest.fn();
jest.mock('react-router-dom', () => ({
  ...jest.requireActual('react-router-dom'),
  useNavigate: () => mockNavigate,
  useLocation: () => ({ state: null }),
}));

const renderLogin = () => {
  return render(
    <BrowserRouter>
      <ThemeProvider>
        <AuthProvider>
          <Login />
        </AuthProvider>
      </ThemeProvider>
    </BrowserRouter>
  );
};

describe('Login Page', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders login form correctly', () => {
    renderLogin();

    expect(screen.getByText('Sign in to your account')).toBeInTheDocument();
    expect(screen.getByLabelText(/member id or email/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/password/i)).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('shows validation errors for empty fields', async () => {
    const user = userEvent.setup();
    renderLogin();

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/member id or email is required/i)).toBeInTheDocument();
      expect(screen.getByText(/password must be at least 6 characters/i)).toBeInTheDocument();
    });
  });

  it('validates member ID format', async () => {
    const user = userEvent.setup();
    renderLogin();

    const identifierInput = screen.getByLabelText(/member id or email/i);
    await user.type(identifierInput, 'ab');

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/please enter a valid email or member id/i)).toBeInTheDocument();
    });
  });

  it('accepts valid email format', async () => {
    const user = userEvent.setup();
    renderLogin();

    const identifierInput = screen.getByLabelText(/member id or email/i);
    const passwordInput = screen.getByLabelText(/password/i);

    await user.type(identifierInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');

    // Should not show validation errors for valid inputs
    expect(screen.queryByText(/please enter a valid email or member id/i)).not.toBeInTheDocument();
  });

  it('accepts valid member ID format', async () => {
    const user = userEvent.setup();
    renderLogin();

    const identifierInput = screen.getByLabelText(/member id or email/i);
    const passwordInput = screen.getByLabelText(/password/i);

    await user.type(identifierInput, 'MEM001');
    await user.type(passwordInput, 'password123');

    // Should not show validation errors for valid inputs
    expect(screen.queryByText(/please enter a valid email or member id/i)).not.toBeInTheDocument();
  });

  it('shows password strength indicator', async () => {
    const user = userEvent.setup();
    renderLogin();

    const passwordInput = screen.getByLabelText(/password/i);
    await user.type(passwordInput, 'weak');

    await waitFor(() => {
      expect(screen.getByText(/password strength/i)).toBeInTheDocument();
    });
  });

  it('toggles password visibility', async () => {
    const user = userEvent.setup();
    renderLogin();

    const passwordInput = screen.getByLabelText(/password/i);
    const toggleButton = screen.getByRole('button', { name: /show password/i });

    expect(passwordInput).toHaveAttribute('type', 'password');

    await user.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'text');

    await user.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  it('shows remember me checkbox', () => {
    renderLogin();

    const rememberMeCheckbox = screen.getByRole('checkbox', { name: /remember me/i });
    expect(rememberMeCheckbox).toBeInTheDocument();
    expect(rememberMeCheckbox).not.toBeChecked();
  });

  it('opens forgot password modal', async () => {
    const user = userEvent.setup();
    renderLogin();

    const forgotPasswordLink = screen.getByText(/forgot password/i);
    await user.click(forgotPasswordLink);

    await waitFor(() => {
      expect(screen.getByText(/reset password/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/email address/i)).toBeInTheDocument();
    });
  });

  it('closes forgot password modal', async () => {
    const user = userEvent.setup();
    renderLogin();

    // Open modal
    const forgotPasswordLink = screen.getByText(/forgot password/i);
    await user.click(forgotPasswordLink);

    await waitFor(() => {
      expect(screen.getByText(/reset password/i)).toBeInTheDocument();
    });

    // Close modal
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    await user.click(cancelButton);

    await waitFor(() => {
      expect(screen.queryByText(/reset password/i)).not.toBeInTheDocument();
    });
  });

  it('shows advertisement section', () => {
    renderLogin();

    expect(screen.getByText(/advertisement space managed by admin/i)).toBeInTheDocument();
  });

  it('shows contact branch manager link', () => {
    renderLogin();

    expect(screen.getByText(/contact your branch manager/i)).toBeInTheDocument();
  });

  it('disables submit button when form is invalid', async () => {
    renderLogin();

    const submitButton = screen.getByRole('button', { name: /sign in/i });
    expect(submitButton).toBeDisabled();
  });

  it('enables submit button when form is valid', async () => {
    const user = userEvent.setup();
    renderLogin();

    const identifierInput = screen.getByLabelText(/member id or email/i);
    const passwordInput = screen.getByLabelText(/password/i);

    await user.type(identifierInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');

    await waitFor(() => {
      const submitButton = screen.getByRole('button', { name: /sign in/i });
      expect(submitButton).not.toBeDisabled();
    });
  });

  it('passes accessibility checks', () => {
    const { container } = renderLogin();
    const accessibilityResults = runAccessibilityChecks(container);

    // Should have minimal accessibility issues
    expect(accessibilityResults.issueCount).toBeLessThan(5);
    
    // Check for proper form labels
    expect(accessibilityResults.ariaLabels.filter(issue => 
      issue.includes('Form input') && issue.includes('lacks proper label')
    )).toHaveLength(0);
  });

  it('supports keyboard navigation', async () => {
    const user = userEvent.setup();
    renderLogin();

    // Tab through form elements
    await user.tab();
    expect(screen.getByLabelText(/member id or email/i)).toHaveFocus();

    await user.tab();
    expect(screen.getByLabelText(/password/i)).toHaveFocus();

    await user.tab();
    expect(screen.getByRole('checkbox', { name: /remember me/i })).toHaveFocus();

    await user.tab();
    expect(screen.getByText(/forgot password/i)).toHaveFocus();

    await user.tab();
    expect(screen.getByRole('button', { name: /sign in/i })).toHaveFocus();
  });

  it('handles form submission with Enter key', async () => {
    const user = userEvent.setup();
    renderLogin();

    const identifierInput = screen.getByLabelText(/member id or email/i);
    const passwordInput = screen.getByLabelText(/password/i);

    await user.type(identifierInput, '<EMAIL>');
    await user.type(passwordInput, 'password123');
    await user.keyboard('{Enter}');

    // Should attempt to submit the form
    await waitFor(() => {
      expect(mockNavigate).toHaveBeenCalled();
    });
  });
});
