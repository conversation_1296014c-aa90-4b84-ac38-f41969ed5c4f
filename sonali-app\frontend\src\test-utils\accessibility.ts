/**
 * Accessibility testing utilities
 */

export const checkKeyboardNavigation = (element: HTMLElement): boolean => {
  // Check if element is focusable
  const focusableElements = [
    'button',
    'input',
    'select',
    'textarea',
    'a[href]',
    '[tabindex]:not([tabindex="-1"])',
  ];

  const isFocusable = focusableElements.some(selector => 
    element.matches(selector) || element.querySelector(selector)
  );

  return isFocusable;
};

export const checkAriaLabels = (element: HTMLElement): string[] => {
  const issues: string[] = [];

  // Check for buttons without accessible names
  const buttons = element.querySelectorAll('button');
  buttons.forEach((button, index) => {
    const hasAccessibleName = 
      button.textContent?.trim() ||
      button.getAttribute('aria-label') ||
      button.getAttribute('aria-labelledby') ||
      button.querySelector('img')?.getAttribute('alt');

    if (!hasAccessibleName) {
      issues.push(`Button at index ${index} lacks accessible name`);
    }
  });

  // Check for form inputs without labels
  const inputs = element.querySelectorAll('input, select, textarea');
  inputs.forEach((input, index) => {
    const hasLabel = 
      input.getAttribute('aria-label') ||
      input.getAttribute('aria-labelledby') ||
      element.querySelector(`label[for="${input.id}"]`) ||
      input.closest('label');

    if (!hasLabel && input.getAttribute('type') !== 'hidden') {
      issues.push(`Form input at index ${index} lacks proper label`);
    }
  });

  // Check for images without alt text
  const images = element.querySelectorAll('img');
  images.forEach((img, index) => {
    if (!img.getAttribute('alt') && img.getAttribute('role') !== 'presentation') {
      issues.push(`Image at index ${index} lacks alt text`);
    }
  });

  return issues;
};

export const checkColorContrast = (element: HTMLElement): string[] => {
  const issues: string[] = [];
  
  // This is a simplified check - in a real app, you'd use a proper contrast checking library
  const textElements = element.querySelectorAll('*');
  
  textElements.forEach((el, index) => {
    const styles = window.getComputedStyle(el);
    const color = styles.color;
    const backgroundColor = styles.backgroundColor;
    
    // Basic check for very light text on light backgrounds or very dark on dark
    if (color === 'rgb(255, 255, 255)' && backgroundColor === 'rgb(255, 255, 255)') {
      issues.push(`Element at index ${index} may have insufficient color contrast`);
    }
    if (color === 'rgb(0, 0, 0)' && backgroundColor === 'rgb(0, 0, 0)') {
      issues.push(`Element at index ${index} may have insufficient color contrast`);
    }
  });

  return issues;
};

export const checkSemanticHTML = (element: HTMLElement): string[] => {
  const issues: string[] = [];

  // Check for proper heading hierarchy
  const headings = element.querySelectorAll('h1, h2, h3, h4, h5, h6');
  let lastLevel = 0;
  
  headings.forEach((heading, index) => {
    const level = parseInt(heading.tagName.charAt(1));
    if (index === 0 && level !== 1) {
      issues.push('First heading should be h1');
    }
    if (level > lastLevel + 1) {
      issues.push(`Heading level skipped at index ${index} (h${lastLevel} to h${level})`);
    }
    lastLevel = level;
  });

  // Check for proper list structure
  const listItems = element.querySelectorAll('li');
  listItems.forEach((li, index) => {
    const parent = li.parentElement;
    if (!parent || !['UL', 'OL'].includes(parent.tagName)) {
      issues.push(`List item at index ${index} is not inside ul or ol`);
    }
  });

  // Check for proper table structure
  const tables = element.querySelectorAll('table');
  tables.forEach((table, index) => {
    const hasCaption = table.querySelector('caption');
    const hasHeaders = table.querySelector('th');
    
    if (!hasCaption) {
      issues.push(`Table at index ${index} lacks caption`);
    }
    if (!hasHeaders) {
      issues.push(`Table at index ${index} lacks header cells`);
    }
  });

  return issues;
};

export const runAccessibilityChecks = (element: HTMLElement) => {
  const results = {
    keyboardNavigation: checkKeyboardNavigation(element),
    ariaLabels: checkAriaLabels(element),
    colorContrast: checkColorContrast(element),
    semanticHTML: checkSemanticHTML(element),
  };

  const allIssues = [
    ...results.ariaLabels,
    ...results.colorContrast,
    ...results.semanticHTML,
  ];

  return {
    ...results,
    hasIssues: allIssues.length > 0,
    issueCount: allIssues.length,
    allIssues,
  };
};
