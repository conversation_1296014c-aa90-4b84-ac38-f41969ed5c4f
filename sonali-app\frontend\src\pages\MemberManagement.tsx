import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>dal,
  ModalHeader,
  ModalBody,
  Alert
} from '../components/ui';
import {
  MemberRegistrationForm,
  MemberList,
  MemberDetails
} from '../components/members';
import { Member, Branch, UpdateMemberData } from '../types';
import { useAuth } from '../contexts/AuthContext';
import MemberService from '../services/memberService';

type ViewMode = 'list' | 'register' | 'details' | 'edit';

export const MemberManagement: React.FC = () => {
  const { user } = useAuth();
  const [viewMode, setViewMode] = useState<ViewMode>('list');
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [bulkLoading, setBulkLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Load branches on component mount
  useEffect(() => {
    loadBranches();
  }, []);

  const loadBranches = async () => {
    try {
      // In a real implementation, you would have a branch service
      // For now, we'll use mock data
      setBranches([
        { id: '1', name: 'Dhaka Main Branch', address: 'Dhaka', isActive: true, createdAt: '', updatedAt: '' },
        { id: '2', name: 'Chittagong Branch', address: 'Chittagong', isActive: true, createdAt: '', updatedAt: '' }
      ]);
    } catch (error) {
      console.error('Error loading branches:', error);
    }
  };

  const handleMemberSelect = (member: Member) => {
    setSelectedMember(member);
    setViewMode('details');
  };

  const handleMemberEdit = (member: Member) => {
    setSelectedMember(member);
    setViewMode('edit');
  };

  const handleMemberDelete = async (member: Member) => {
    try {
      await MemberService.deleteMember(member.id);
      setSuccess(`Member ${member.name} has been deleted successfully.`);
      setViewMode('list');
      setSelectedMember(null);
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to delete member');
    }
  };

  const handleRegistrationSuccess = (member: Member) => {
    setSuccess(`Member ${member.name} has been registered successfully!`);
    setViewMode('list');
  };

  const handleBulkActivate = async () => {
    setBulkLoading(true);
    try {
      const updateData: UpdateMemberData = { isActive: true };
      await MemberService.bulkUpdateMembers(selectedMembers, updateData);
      setSuccess(`${selectedMembers.length} members have been activated.`);
      setSelectedMembers([]);
      setShowBulkActions(false);
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to activate members');
    } finally {
      setBulkLoading(false);
    }
  };

  const handleBulkDeactivate = async () => {
    setBulkLoading(true);
    try {
      const updateData: UpdateMemberData = { isActive: false };
      await MemberService.bulkUpdateMembers(selectedMembers, updateData);
      setSuccess(`${selectedMembers.length} members have been deactivated.`);
      setSelectedMembers([]);
      setShowBulkActions(false);
    } catch (error: any) {
      setError(error.response?.data?.message || 'Failed to deactivate members');
    } finally {
      setBulkLoading(false);
    }
  };

  const renderHeader = () => (
    <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-6">
      <div>
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Member Management
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Manage member registrations, profiles, and information
        </p>
      </div>
      
      <div className="flex gap-2">
        {selectedMembers.length > 0 && (
          <Button
            variant="secondary"
            onClick={() => setShowBulkActions(true)}
          >
            Bulk Actions ({selectedMembers.length})
          </Button>
        )}
        
        {viewMode !== 'register' && (
          <Button
            variant="primary"
            onClick={() => setViewMode('register')}
          >
            ➕ Register Member
          </Button>
        )}
        
        {viewMode !== 'list' && (
          <Button
            variant="secondary"
            onClick={() => {
              setViewMode('list');
              setSelectedMember(null);
            }}
          >
            📋 View List
          </Button>
        )}
      </div>
    </div>
  );

  const renderContent = () => {
    switch (viewMode) {
      case 'register':
        return (
          <MemberRegistrationForm
            onSuccess={handleRegistrationSuccess}
            onCancel={() => setViewMode('list')}
            branches={branches}
          />
        );
        
      case 'details':
        return selectedMember ? (
          <MemberDetails
            memberId={selectedMember.id}
            onEdit={handleMemberEdit}
            onDelete={handleMemberDelete}
            onClose={() => setViewMode('list')}
          />
        ) : null;
        
      case 'edit':
        return selectedMember ? (
          <MemberRegistrationForm
            initialData={selectedMember}
            onSuccess={() => {
              setSuccess('Member updated successfully!');
              setViewMode('details');
            }}
            onCancel={() => setViewMode('details')}
            branches={branches}
          />
        ) : null;
        
      default:
        return (
          <MemberList
            onMemberSelect={handleMemberSelect}
            onMemberEdit={handleMemberEdit}
            onMemberDelete={handleMemberDelete}
            branches={branches}
            selectable={true}
            selectedMembers={selectedMembers}
            onSelectionChange={setSelectedMembers}
          />
        );
    }
  };

  return (
    <div className="space-y-6">
      {/* Alerts */}
      {error && (
        <Alert 
          type="error" 
          title="Error" 
          description={error}
          onDismiss={() => setError(null)}
        />
      )}
      
      {success && (
        <Alert 
          type="success" 
          title="Success" 
          description={success}
          onDismiss={() => setSuccess(null)}
        />
      )}

      {/* Header */}
      {renderHeader()}

      {/* Content */}
      {renderContent()}

      {/* Bulk Actions Modal */}
      <Modal isOpen={showBulkActions} onClose={() => setShowBulkActions(false)}>
        <ModalHeader>
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Bulk Actions
          </h3>
        </ModalHeader>
        <ModalBody>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            You have selected {selectedMembers.length} member(s). Choose an action:
          </p>
          
          <div className="space-y-3">
            <Button
              variant="primary"
              onClick={handleBulkActivate}
              disabled={bulkLoading}
              className="w-full"
            >
              {bulkLoading ? 'Processing...' : '✅ Activate Selected Members'}
            </Button>
            
            <Button
              variant="secondary"
              onClick={handleBulkDeactivate}
              disabled={bulkLoading}
              className="w-full"
            >
              {bulkLoading ? 'Processing...' : '❌ Deactivate Selected Members'}
            </Button>
            
            <Button
              variant="secondary"
              onClick={() => {
                setSelectedMembers([]);
                setShowBulkActions(false);
              }}
              disabled={bulkLoading}
              className="w-full"
            >
              Clear Selection
            </Button>
          </div>
        </ModalBody>
      </Modal>
    </div>
  );
};

export default MemberManagement;
