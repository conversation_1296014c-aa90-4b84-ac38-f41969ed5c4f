import { api } from '../utils/api';
import {
  Member,
  CreateMemberData,
  UpdateMemberData,
  MemberListQuery,
  MemberSearchQuery,
  MemberSearchResult,
  PaginatedResponse,
  ApiResponse
} from '../types';

/**
 * Member Service
 * Handles all member-related API calls
 */
export class MemberService {
  
  /**
   * Get paginated list of members
   */
  static async getMembers(query: MemberListQuery = {}): Promise<PaginatedResponse<Member>> {
    try {
      const params = new URLSearchParams();
      
      if (query.page) params.append('page', query.page.toString());
      if (query.limit) params.append('limit', query.limit.toString());
      if (query.search) params.append('search', query.search);
      if (query.branchId) params.append('branchId', query.branchId);
      if (query.isActive !== undefined) params.append('isActive', query.isActive.toString());
      if (query.sortBy) params.append('sortBy', query.sortBy);
      if (query.sortOrder) params.append('sortOrder', query.sortOrder);

      const response = await api.get(`/members?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching members:', error);
      throw error;
    }
  }

  /**
   * Search members with autocomplete
   */
  static async searchMembers(query: MemberSearchQuery): Promise<MemberSearchResult[]> {
    try {
      const params = new URLSearchParams();
      params.append('query', query.query);
      if (query.branchId) params.append('branchId', query.branchId);
      if (query.limit) params.append('limit', query.limit.toString());

      const response = await api.get(`/members/search?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error searching members:', error);
      throw error;
    }
  }

  /**
   * Get member by ID
   */
  static async getMemberById(id: string): Promise<Member> {
    try {
      const response = await api.get(`/members/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching member:', error);
      throw error;
    }
  }

  /**
   * Create new member
   */
  static async createMember(memberData: CreateMemberData): Promise<Member> {
    try {
      const response = await api.post('/members', memberData);
      return response.data;
    } catch (error) {
      console.error('Error creating member:', error);
      throw error;
    }
  }

  /**
   * Update member
   */
  static async updateMember(id: string, memberData: UpdateMemberData): Promise<Member> {
    try {
      const response = await api.put(`/members/${id}`, memberData);
      return response.data;
    } catch (error) {
      console.error('Error updating member:', error);
      throw error;
    }
  }

  /**
   * Delete member (soft delete)
   */
  static async deleteMember(id: string): Promise<void> {
    try {
      await api.delete(`/members/${id}`);
    } catch (error) {
      console.error('Error deleting member:', error);
      throw error;
    }
  }

  /**
   * Upload member photo
   */
  static async uploadPhoto(file: File): Promise<string> {
    try {
      const formData = new FormData();
      formData.append('photo', file);

      const response = await api.post('/members/upload-photo', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      
      return response.data.url;
    } catch (error) {
      console.error('Error uploading photo:', error);
      throw error;
    }
  }

  /**
   * Generate member ID
   */
  static async generateMemberId(branchId?: string): Promise<string> {
    try {
      const params = branchId ? `?branchId=${branchId}` : '';
      const response = await api.get(`/members/generate-id${params}`);
      return response.data.memberId;
    } catch (error) {
      console.error('Error generating member ID:', error);
      throw error;
    }
  }

  /**
   * Export members to Excel
   */
  static async exportMembers(query: MemberListQuery = {}): Promise<Blob> {
    try {
      const params = new URLSearchParams();
      
      if (query.search) params.append('search', query.search);
      if (query.branchId) params.append('branchId', query.branchId);
      if (query.isActive !== undefined) params.append('isActive', query.isActive.toString());

      const response = await api.get(`/members/export?${params.toString()}`, {
        responseType: 'blob',
      });
      
      return response.data;
    } catch (error) {
      console.error('Error exporting members:', error);
      throw error;
    }
  }

  /**
   * Bulk update members
   */
  static async bulkUpdateMembers(memberIds: string[], updateData: UpdateMemberData): Promise<void> {
    try {
      await api.put('/members/bulk-update', {
        memberIds,
        updateData,
      });
    } catch (error) {
      console.error('Error bulk updating members:', error);
      throw error;
    }
  }

  /**
   * Get member statistics
   */
  static async getMemberStats(branchId?: string): Promise<{
    total: number;
    active: number;
    inactive: number;
    thisMonth: number;
  }> {
    try {
      const params = branchId ? `?branchId=${branchId}` : '';
      const response = await api.get(`/members/stats${params}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching member stats:', error);
      throw error;
    }
  }

  /**
   * Validate member ID uniqueness
   */
  static async validateMemberId(memberId: string, excludeId?: string): Promise<boolean> {
    try {
      const params = new URLSearchParams();
      params.append('memberId', memberId);
      if (excludeId) params.append('excludeId', excludeId);

      const response = await api.get(`/members/validate-id?${params.toString()}`);
      return response.data.isValid;
    } catch (error) {
      console.error('Error validating member ID:', error);
      return false;
    }
  }

  /**
   * Validate NID uniqueness
   */
  static async validateNid(nidNumber: string, excludeId?: string): Promise<boolean> {
    try {
      const params = new URLSearchParams();
      params.append('nidNumber', nidNumber);
      if (excludeId) params.append('excludeId', excludeId);

      const response = await api.get(`/members/validate-nid?${params.toString()}`);
      return response.data.isValid;
    } catch (error) {
      console.error('Error validating NID:', error);
      return false;
    }
  }

  /**
   * Print member card
   */
  static async printMemberCard(memberId: string): Promise<Blob> {
    try {
      const response = await api.get(`/members/${memberId}/card`, {
        responseType: 'blob',
      });
      
      return response.data;
    } catch (error) {
      console.error('Error printing member card:', error);
      throw error;
    }
  }

  /**
   * Get members for reference selection
   */
  static async getMembersForReference(branchId?: string, excludeId?: string): Promise<MemberSearchResult[]> {
    try {
      const params = new URLSearchParams();
      if (branchId) params.append('branchId', branchId);
      if (excludeId) params.append('excludeId', excludeId);

      const response = await api.get(`/members/for-reference?${params.toString()}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching members for reference:', error);
      throw error;
    }
  }
}

export default MemberService;
