import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, Loading, Alert } from '../ui';
import { MetricCard, QuickActionButton, ActivityTimeline, PerformanceChart } from './';
import { AdminDashboardData, DashboardMetric } from '../../types';
import DashboardService from '../../services/dashboardService';

export const AdminDashboard: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<AdminDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        const data = await DashboardService.getAdminDashboard();
        setDashboardData(data);
        setError(null);
      } catch (err) {
        setError('Failed to load dashboard data');
        console.error('Error fetching admin dashboard:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-32 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse"></div>
          ))}
        </div>
        <Loading size="lg" />
      </div>
    );
  }

  if (error || !dashboardData) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 text-lg mb-4">⚠️ {error || 'Failed to load dashboard'}</div>
        <button 
          onClick={() => window.location.reload()} 
          className="px-4 py-2 bg-primary-600 text-white rounded hover:bg-primary-700"
        >
          Retry
        </button>
      </div>
    );
  }

  const { metrics, financialPerformance, branchPerformance, recentActivities, quickActions, alerts } = dashboardData;

  // Convert metrics to DashboardMetric format
  const systemMetrics: DashboardMetric[] = [
    {
      label: 'Total Branches',
      value: metrics.totalBranches,
      icon: 'building',
      color: 'primary'
    },
    {
      label: 'Total Users',
      value: metrics.totalUsers,
      icon: 'users',
      color: 'info'
    },
    {
      label: 'Field Officers',
      value: metrics.totalFieldOfficers,
      icon: 'users',
      color: 'success'
    },
    {
      label: 'Total Members',
      value: metrics.totalMembers,
      icon: 'users',
      color: 'warning'
    }
  ];

  const financialMetrics: DashboardMetric[] = [
    {
      label: 'Active Loans',
      value: metrics.totalActiveLoans,
      icon: 'credit-card',
      color: 'primary'
    },
    {
      label: 'Total Loan Amount',
      value: metrics.totalLoanAmount,
      icon: 'dollar-sign',
      color: 'success'
    },
    {
      label: 'Total Collections',
      value: metrics.totalCollections,
      icon: 'trending-up',
      color: 'info'
    },
    {
      label: 'System Uptime',
      value: metrics.systemUptime,
      icon: 'check-circle',
      color: 'success'
    }
  ];

  const unreadAlerts = alerts.filter(alert => !alert.isRead);

  return (
    <div className="space-y-6">
      {/* System Alerts */}
      {unreadAlerts.length > 0 && (
        <div className="space-y-2">
          {unreadAlerts.slice(0, 2).map((alert) => (
            <Alert
              key={alert.id}
              type={alert.type}
              title={alert.title}
              description={alert.message}
            />
          ))}
          {unreadAlerts.length > 2 && (
            <div className="text-center">
              <button className="text-sm text-primary-600 dark:text-primary-400 hover:underline">
                View {unreadAlerts.length - 2} more alerts
              </button>
            </div>
          )}
        </div>
      )}

      {/* System Overview Metrics */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">System Overview</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {systemMetrics.map((metric, index) => (
            <MetricCard key={index} metric={metric} />
          ))}
        </div>
      </div>

      {/* Financial Overview Metrics */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">Financial Overview</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {financialMetrics.map((metric, index) => (
            <MetricCard key={index} metric={metric} />
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <span>⚡</span>
            Admin Quick Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {quickActions.map((action) => (
              <QuickActionButton key={action.id} action={action} size="sm" />
            ))}
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Financial Performance Chart */}
        <PerformanceChart
          data={financialPerformance}
          title="Financial Performance Trends"
          type="line"
          height={300}
        />

        {/* Branch Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>🏢</span>
              Branch Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {branchPerformance.map((branch) => (
                <div key={branch.id} className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="flex justify-between items-start mb-2">
                    <div>
                      <h4 className="font-medium text-gray-900 dark:text-white">{branch.name}</h4>
                      <p className="text-sm text-gray-600 dark:text-gray-400">Manager: {branch.managerName}</p>
                    </div>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                      branch.performance >= 90 ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400' :
                      branch.performance >= 75 ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400' :
                      'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                    }`}>
                      {branch.performance.toFixed(1)}%
                    </span>
                  </div>
                  <div className="grid grid-cols-3 gap-4 text-sm text-gray-600 dark:text-gray-400 mb-2">
                    <div>Members: {branch.totalMembers}</div>
                    <div>Loans: {branch.activeLoans}</div>
                    <div>Collections: ৳{branch.collectionsThisMonth.toLocaleString()}</div>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div 
                      className={`h-2 rounded-full ${
                        branch.performance >= 90 ? 'bg-green-500' :
                        branch.performance >= 75 ? 'bg-yellow-500' :
                        'bg-red-500'
                      }`}
                      style={{ width: `${branch.performance}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent System Activities */}
        <ActivityTimeline 
          activities={recentActivities} 
          title="Recent System Activities"
          maxItems={6}
        />

        {/* System Health & Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <span>📊</span>
              System Health & Statistics
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-lg font-bold text-green-600 dark:text-green-400">
                    {metrics.systemUptime}
                  </div>
                  <div className="text-xs text-green-700 dark:text-green-300">System Uptime</div>
                </div>
                <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
                    {metrics.totalActiveLoans}
                  </div>
                  <div className="text-xs text-blue-700 dark:text-blue-300">Active Loans</div>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Loan Portfolio Health</span>
                    <span>92.5%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '92.5%' }}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Collection Efficiency</span>
                    <span>87.3%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-yellow-500 h-2 rounded-full" style={{ width: '87.3%' }}></div>
                  </div>
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span>Member Satisfaction</span>
                    <span>94.1%</span>
                  </div>
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: '94.1%' }}></div>
                  </div>
                </div>
              </div>

              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                  <div className="flex justify-between">
                    <span>Total Portfolio Value:</span>
                    <span className="font-medium">৳{metrics.totalLoanAmount.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Monthly Collections:</span>
                    <span className="font-medium">৳{metrics.totalCollections.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Average Loan Size:</span>
                    <span className="font-medium">৳{Math.round(metrics.totalLoanAmount / metrics.totalActiveLoans).toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;
